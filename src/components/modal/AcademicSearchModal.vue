<template>
  <el-dialog
    v-model="visible"
    title="学术搜索"
    width="70%"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="academic-search-modal"
    align-center
    @close="handleCancel"
  >
    <!-- 顶部引用文本块 -->
    <div class="reference-quote">
      <!-- 引导条区域 -->
      <div class="quote-indicator"></div>
      <div class="quote-text">
        {{ referenceText }}
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-loading="loading">
      <!-- 左侧学术文献列表 -->
      <div class="literature-list" v-if="literatureList.length > 0">
        <!-- 文献条目循环 -->
        <div
          v-for="item in literatureList"
          :key="item.index"
          class="literature-item"
          :class="{ 'literature-item-selected': selectedPaper === item.index }"
          @click="selectPaper(item.index)"
        >
          <div class="literature-content">
            <div class="literature-index">{{ item.index }}</div>
            <div class="literature-details" v-if="item.abstract">
              <div class="literature-text">
                {{ item.abstract }}
              </div>
              <div class="literature-meta">
                <div class="literature-tag" v-if="item.pdf_url">
                  <img
                    src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/pdf-icon.png"
                  />
                </div>
                <span class="literature-citation">
                  {{ item.authors }}.{{ item.title }}. {{ item.publication }},{{ item.year }}.
                </span>
              </div>
              <div class="literature-actions" v-if="item.pdf_url">
                <el-button
                  :loading="currentJoinKnowledgeUrl === item.pdf_url && joinKnowledgeLoading"
                  type="primary"
                  size="small"
                  style="padding: 4px 12px"
                  class="btn-normal"
                  @click.stop="handleAddKnowledge(item.title, item.pdf_url)"
                >
                  <el-icon><View /></el-icon>
                  <span style="margin-left: 5px">加入知识库</span>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="not-more-data">暂无更多参考资料</div>
      </div>
      <div v-else class="empty">
        <div v-if="isSearchCountDeficiency">
          学术搜索次数不足，请先<el-button
            type="primary"
            style="margin-left: 10px"
            @click="handleRecharge"
            >兑换</el-button
          >
        </div>
        <div v-else-if="!loading">暂无数据</div>
      </div>
    </div>

    <!-- 文本润色区域 -->
    <div class="text-polish" v-loading="polishLoading" v-if="selectedPaper">
      <div v-if="polishContent">
        <h3 class="polish-title">文本润色</h3>
        <div class="polish-input">{{ polishContent }}</div>
      </div>
      <div class="polish-actions">
        <div class="references-option">
          <el-checkbox v-model="includeReferences">插入上标及尾部参考文献</el-checkbox>
        </div>

        <div class="action-buttons">
          <el-button type="primary" class="btn-normal" @click="handleInsert">插入</el-button>
          <el-button type="primary" class="btn-normal" @click="handleTransform">替换</el-button>
        </div>
      </div>
    </div>

    <ExchangeSearchModal
      v-if="rechargeSearchCountVisible"
      v-model:visible="rechargeSearchCountVisible"
      @success="onSuccess"
      @recharge="onRecharge"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { View } from '@element-plus/icons-vue'
import { addFiles, searchScholar } from '@/api/repositoryFile'
import type { SearchScholar } from '@/services/types/repositoryFile'
import { doAiAction } from '@/api/submissionEdit'
import { useUserStore } from '@/stores/user'
import ExchangeSearchModal from '@/components/modal/ExchangeSearchModal.vue'
import { ACTION_CODE, AddToDefaultFolder, HTTP_STATUS, TEAM_MEMBER_ROLE } from '@/utils/constants'
import { uploadByUrl } from '@/api/upload'

const props = defineProps<{
  modelValue: boolean
  referenceText?: string
  submissionId?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (
    e: 'confirm',
    value: {
      action: string
      includeReferences: boolean
      currentLiteratrueData?: string
      polishContent?: string
    }
  ): void
  (e: 'recharge', value: number): void
}>()

// 计算属性控制弹窗显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const rechargeSearchCountVisible = ref(false)

const loading = ref(false)
const polishLoading = ref(false)
const joinKnowledgeLoading = ref(false)

const isSearchCountDeficiency = ref(false)

const polishContent = ref()
const currentJoinKnowledgeUrl = ref()

// 假数据数组
const literatureList = ref<SearchScholar[]>([])

// 响应式数据
const selectedPaper = ref<number | null>(null)
const includeReferences = ref(false)

const store = useUserStore()

const getIsTeamIdentity = computed(() => store.getTeamId)

// 选择学术文献
const selectPaper = (paperIndex: number) => {
  selectedPaper.value = paperIndex

  loadPolistData()
}

const handleRecharge = () => {
  rechargeSearchCountVisible.value = true
}

// 监听弹窗打开状态，重置表单
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 重置状态
      selectedPaper.value = null
      includeReferences.value = false
    }
  }
)

const currentLiteratrueData = computed(() => {
  const list = literatureList.value.filter((item) => item.index == selectedPaper.value)
  if (list.length == 0) {
    return
  }
  let data = `${list[0].authors}.${list[0].title}.`
  if (list[0].publication) {
    return `${data}${list[0].publication},${list[0].year}.`
  }
  return `${data}${list[0].year}.`
})

// 插入操作
const handleInsert = () => {
  emit('confirm', {
    action: 'insert',
    includeReferences: includeReferences.value,
    currentLiteratrueData: currentLiteratrueData.value || undefined,
    polishContent: polishContent.value || undefined
  })

  emit('update:modelValue', false)
  ElMessage.success('已插入学术内容')
}

// 转换操作
const handleTransform = () => {
  emit('confirm', {
    action: 'replace',
    includeReferences: includeReferences.value,
    currentLiteratrueData: currentLiteratrueData.value || undefined,
    polishContent: polishContent.value || undefined
  })

  emit('update:modelValue', false)
  ElMessage.success('已转换文本内容')
}

// 取消操作
const handleCancel = () => {
  emit('update:modelValue', false)
}

const handleAddKnowledge = async (title: string, url: string) => {
  try {
    currentJoinKnowledgeUrl.value = url
    joinKnowledgeLoading.value = true
    const params = {
      fileName: `${title || '学术搜索文档'}.pdf`,
      fileUrl: url
    }

    const fileData = await uploadByUrl(params)
    if (!fileData.ok || !fileData.data) {
      ElMessage.error(fileData.message || '文件上传失败')
      return
    }

    // 6. 添加到知识库
    const _response = {
      // folderId: 0,
      fileIds: [fileData.data.id],
      fileCategory: AddToDefaultFolder.SCHOLAR,
      spaceId: store.currentLoginInfo?.id
    }
    const res = await addFiles(_response)
    if (!res.ok) {
      ElMessage.error(res.message || '文件添加到知识库失败')
      return
    }

    ElMessage.success('文件添加到知识库成功')
  } catch (error) {
    console.log('error ==>', error)
  } finally {
    joinKnowledgeLoading.value = false
    currentJoinKnowledgeUrl.value = ''
  }
}

const setupDate = async () => {
  try {
    if (!props.referenceText) {
      ElMessage.warning('请先选择需要进行学术搜索的文本内容')
      return
    }
    loading.value = true
    const res = await searchScholar({
      query: props.referenceText,
      page: 1,
      teamId: store.getTeamId,
      submissionId: props.submissionId || ''
    })
    // console.log('res ==>', res)
    if (res.code == HTTP_STATUS.ACADEMIC_SEARCH_COUNT_INSUFFICIENT) {
      rechargeSearchCountVisible.value = true
      isSearchCountDeficiency.value = true
      return
    }
    if (!res.ok || !res.data.records || res.data.records.length == 0) {
      return
    }
    literatureList.value = res.data.records || []
    selectedPaper.value = res.data.records[0].index
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const loadPolistData = async () => {
  try {
    polishLoading.value = true
    let abstract = ''
    const list = literatureList.value.filter((item) => item.index == selectedPaper.value)
    if (list.length > 0) {
      abstract = list[0].abstract
    }
    const res = await doAiAction({
      teamId: store.getTeamId,
      submissionId: props.submissionId || '',
      code: ACTION_CODE.EDITOR_REWRITE,
      content: props.referenceText || '',
      params: {
        ask: abstract
      }
    })
    // console.log('polist res ==>', res)
    if (!res.success || !res.data) {
      ElMessage.error(res.message || '文本润色失败')
      return
    }
    polishContent.value = res.data
  } catch (error) {
    console.log('error ==>', error)
  } finally {
    polishLoading.value = false
  }
}

const onRecharge = () => {
  if (getIsTeamIdentity.value) {
    if (store.currentTeam?.member.role == TEAM_MEMBER_ROLE.OWNER) {
      emit('recharge', 1)
    } else {
      ElMessage.warning('请联系团队创建者充值')
    }
    return
  }
  emit('recharge', 1)
}

const onSuccess = async () => {
  await setupDate()
  await loadPolistData()
}

onMounted(async () => {
  await setupDate()
  await loadPolistData()
})
</script>

<style scoped lang="scss">
.academic-search-modal {
  :deep(.el-dialog) {
    max-width: 1200px;
    min-width: 600px;
  }

  :deep(.el-dialog__header) {
    background: #f4f6ff;
  }

  :deep(.el-dialog__body) {
    background: #f4f6ff;
    padding: 20px;
  }
}

// 顶部引用文本块
.reference-quote {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #eeeeee;
  border: 1px solid #eeeeee;
  border-radius: 10px;
}

.quote-indicator {
  width: 8px;
  height: 40px;
  background-color: #888888;
  margin-right: 12px;
  margin-left: 10px;
  border-radius: 2px;
}

.not-more-data {
  text-align: center;
}

.quote-text {
  color: #666666;
  font-size: 15px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 主要内容区域
.main-content {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  height: 388px;
}

// 左侧文献列表
.literature-list {
  width: 65%;
  flex: 1;
  background-color: #eeeeee;
  border: 1px solid #eeeeee;
  padding: 20px;
  border-radius: 8px;
  max-height: 590px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.btn-normal {
  // background: #2551b5;
  // border: none;
}

.empty {
  width: 100%;
  text-align: center;
  margin-top: 50px;
}

.literature-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #eeeeee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #ffffff;

  &:hover {
    border-color: #d1d5db;
  }

  &.literature-item-selected {
    border-color: #2551b5;
    background-color: #e7edfe;
  }
}

.literature-content {
  display: flex;
  align-items: flex-start;
}

.literature-index {
  color: #333333;
  font-size: 15px;
  font-weight: bold;
  line-height: 25px;
  margin-bottom: 10px;
  margin-right: 5px;
}

.literature-details {
  flex: 1;
}

.literature-text {
  color: #333333;
  font-size: 15px;
  font-weight: bold;
  line-height: 25px;
  margin-bottom: 10px;
}

.literature-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.literature-tag {
  width: 21px;
  height: 21px;
  border-radius: 2px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 21px;
    height: 21px;
  }
}

.literature-citation {
  flex: 1;
  font-size: 14px;
}

.literature-actions {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// 右侧详细信息面板
.paper-details {
  width: 35%;
  max-width: 400px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e7e9f5;
}

.details-title {
  color: #1f2937;
  font-weight: 500;
  margin-bottom: 16px;
  font-size: 16px;
}

.details-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.details-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.details-content {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.5;
}

// 文本润色区域
.text-polish {
  margin-top: 20px;
}

.polish-title {
  color: #1f2937;
  font-weight: 500;
  margin-bottom: 16px;
  font-size: 16px;
}

.polish-input {
  margin-bottom: 16px;
  background: #ecf1ff;
  border-radius: 10px;
  padding: 15px 20px;
  line-height: 25px;
  font-size: 15px;
  color: #333333;
  word-break: break-all;
}

.polish-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.references-option {
  display: flex;
  align-items: center;

  :deep(.el-checkbox__label) {
    font-size: 14px;
    color: #374151;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
}
</style>
