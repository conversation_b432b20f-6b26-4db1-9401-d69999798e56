/* Element Plus 主题色覆盖 */
:root {
  /* 主色调系列 */
  --el-color-primary: #2551B5;
  --el-color-primary-light-1: #3D63C2;
  --el-color-primary-light-2: #5575CF;
  --el-color-primary-light-3: #6D87DC;
  --el-color-primary-light-4: #8599E9;
  --el-color-primary-light-5: #9DABF6;
  --el-color-primary-light-6: #B5BDFF;
  --el-color-primary-light-7: #CDCFFF;
  --el-color-primary-light-8: #E5E1FF;
  --el-color-primary-light-9: #F2F0FF;
  
  /* 深色变体 */
  --el-color-primary-dark-1: #2149A3;
  --el-color-primary-dark-2: #1D4191;
  
  /* 按钮相关 */
  --el-button-bg-color: #2551B5;
  --el-button-border-color: #2551B5;
  --el-button-hover-bg-color: #3D63C2;
  --el-button-hover-border-color: #3D63C2;
  --el-button-active-bg-color: #2149A3;
  --el-button-active-border-color: #2149A3;
  
  /* 链接颜色 */
  --el-link-color: #2551B5;
  --el-link-hover-color: #3D63C2;
  
  /* 选择器相关 */
  --el-checkbox-checked-bg-color: #2551B5;
  --el-checkbox-checked-border-color: #2551B5;
  --el-radio-checked-bg-color: #2551B5;
  --el-radio-checked-border-color: #2551B5;
  
  /* 输入框焦点色 */
  --el-input-focus-border-color: #2551B5;
  
  /* 开关组件 */
  --el-switch-on-color: #2551B5;
  
  /* 滑块组件 */
  --el-slider-main-bg-color: #2551B5;
  --el-slider-runway-bg-color: #E8F0F8;
  
  /* 进度条 */
  --el-progress-color: #2551B5;
  
  /* 标签页 */
  --el-tabs-header-color: #2551B5;
  
  /* 菜单激活色 */
  --el-menu-active-color: #2551B5;
  --el-menu-hover-bg-color: #E8F0F8;
}

/* 确保按钮类型为primary时使用新的主题色 */
.el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.el-button--primary:hover {
  background-color: var(--el-color-primary-light-1);
  border-color: var(--el-color-primary-light-1);
}

.el-button--primary:active {
  background-color: var(--el-color-primary-dark-1);
  border-color: var(--el-color-primary-dark-1);
}

/* 单选按钮组激活状态 */
.el-radio-button__inner:hover {
  color: var(--el-color-primary);
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: #fff;
}

/* 对话框确认按钮 */
.el-message-box__btns .el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}
