/* Element Plus 主题色覆盖 - 强制优先级 */
:root {
  /* 主色调系列 */
  --el-color-primary: #2551b5 !important;
  --el-color-primary-light-1: #3d63c2 !important;
  --el-color-primary-light-2: #5575cf !important;
  --el-color-primary-light-3: #6d87dc !important;
  --el-color-primary-light-4: #8599e9 !important;
  --el-color-primary-light-5: #9dabf6 !important;
  --el-color-primary-light-6: #b5bdff !important;
  --el-color-primary-light-7: #cdcfff !important;
  --el-color-primary-light-8: #e5e1ff !important;
  --el-color-primary-light-9: #f2f0ff !important;

  /* 深色变体 */
  --el-color-primary-dark-1: #2149a3 !important;
  --el-color-primary-dark-2: #1d4191 !important;

  /* 按钮相关 */
  --el-button-bg-color: #2551b5 !important;
  --el-button-border-color: #2551b5 !important;
  --el-button-hover-bg-color: #3d63c2 !important;
  --el-button-hover-border-color: #3d63c2 !important;
  --el-button-active-bg-color: #2149a3 !important;
  --el-button-active-border-color: #2149a3 !important;

  /* 链接颜色 */
  --el-link-color: #2551b5;
  --el-link-hover-color: #3d63c2;

  /* 选择器相关 */
  --el-checkbox-checked-bg-color: #2551b5;
  --el-checkbox-checked-border-color: #2551b5;
  --el-radio-checked-bg-color: #2551b5;
  --el-radio-checked-border-color: #2551b5;

  /* 输入框焦点色 */
  --el-input-focus-border-color: #2551b5;

  /* 开关组件 */
  --el-switch-on-color: #2551b5;

  /* 滑块组件 */
  --el-slider-main-bg-color: #2551b5;
  --el-slider-runway-bg-color: #e8f0f8;

  /* 进度条 */
  --el-progress-color: #2551b5;

  /* 标签页 */
  --el-tabs-header-color: #2551b5;

  /* 菜单激活色 */
  --el-menu-active-color: #2551b5;
  --el-menu-hover-bg-color: #e8f0f8;
}

/* 确保按钮类型为primary时使用新的主题色 - 强制优先级 */
.el-button--primary,
.el-button.el-button--primary {
  background-color: #2551b5 !important;
  border-color: #2551b5 !important;
  color: #ffffff !important;
}

.el-button--primary:hover,
.el-button.el-button--primary:hover {
  background-color: #3d63c2 !important;
  border-color: #3d63c2 !important;
  color: #ffffff !important;
}

.el-button--primary:active,
.el-button.el-button--primary:active {
  background-color: #2149a3 !important;
  border-color: #2149a3 !important;
  color: #ffffff !important;
}

/* 单选按钮组激活状态 */
.el-radio-button__inner:hover {
  color: var(--el-color-primary);
}

.el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  color: #fff;
}

/* 对话框确认按钮 */
.el-message-box__btns .el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}
